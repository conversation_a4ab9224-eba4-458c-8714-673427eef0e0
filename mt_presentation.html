<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Machine Translation: SMT and NMT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            padding: 60px 80px;
            display: none;
            flex-direction: column;
            justify-content: center;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .slide.active {
            display: flex;
        }

        .slide h1 {
            font-size: 3em;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 300;
        }

        .slide h2 {
            font-size: 2.5em;
            color: #34495e;
            margin-bottom: 40px;
            text-align: center;
            font-weight: 400;
        }

        .slide h3 {
            font-size: 2em;
            color: #2980b9;
            margin-bottom: 25px;
            font-weight: 500;
        }

        .slide h4 {
            font-size: 1.5em;
            color: #34495e;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .slide p {
            font-size: 1.3em;
            line-height: 1.6;
            margin-bottom: 20px;
            text-align: justify;
        }

        .slide ul {
            font-size: 1.2em;
            line-height: 1.8;
            margin-left: 40px;
            margin-bottom: 20px;
        }

        .slide li {
            margin-bottom: 10px;
        }

        .equation {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 1.4em;
            text-align: center;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            height: 100%;
            align-items: start;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 1.1em;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .comparison-table th {
            background-color: #3498db;
            color: white;
            font-weight: 500;
        }

        .comparison-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0;
        }

        .highlight-box h3 {
            color: white;
            margin-bottom: 15px;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .nav-btn {
            padding: 12px 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: background 0.3s ease;
        }

        .nav-btn:hover {
            background: #2980b9;
        }

        .nav-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .slide-counter {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: #34495e;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 1em;
            z-index: 1000;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: #3498db;
            transition: width 0.3s ease;
            z-index: 1000;
        }

        .architecture-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            margin: 20px 0;
            overflow-x: auto;
        }

        .timeline {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .timeline-item {
            text-align: center;
            flex: 1;
        }

        .timeline-year {
            background: #3498db;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <div class="progress-bar" id="progressBar"></div>
        
        <!-- Slide 1: Title -->
        <div class="slide active">
            <h1>Machine Translation in NLP</h1>
            <div class="highlight-box">
                <h3>Statistical Machine Translation (SMT)</h3>
                <h3>&</h3>
                <h3>Neural Machine Translation (NMT)</h3>
            </div>
            <p style="text-align: center; font-size: 1.4em; margin-top: 30px;">
                A Comprehensive Guide to Modern Translation Systems
            </p>
        </div>

        <!-- Slide 2: Course Overview -->
        <div class="slide">
            <h2>Course Overview</h2>
            <ul>
                <li><strong>Historical Context:</strong> Evolution from rule-based to neural systems</li>
                <li><strong>Statistical MT:</strong> Phrase-based models, alignment, decoding</li>
                <li><strong>Neural MT:</strong> Seq2seq, attention, Transformers</li>
                <li><strong>Comparative Analysis:</strong> Strengths, limitations, performance</li>
                <li><strong>Practical Applications:</strong> Real-world deployment challenges</li>
                <li><strong>Current Trends:</strong> Multilingual models, low-resource MT</li>
            </ul>
        </div>

        <!-- Slide 3: What is Machine Translation? -->
        <div class="slide">
            <h2>What is Machine Translation?</h2>
            <p>Machine Translation (MT) is the automated process of translating text or speech from one natural language to another using computational methods.</p>
            
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-year">1950s</div>
                    <p>Rule-based</p>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">1990s</div>
                    <p>Statistical</p>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">2010s</div>
                    <p>Neural</p>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">Present</div>
                    <p>Transformers</p>
                </div>
            </div>
        </div>

        <!-- Slide 4: Core Challenges -->
        <div class="slide">
            <h2>Core Challenges in Machine Translation</h2>
            <div class="two-column">
                <div>
                    <h4>Lexical Ambiguity</h4>
                    <p>Words have multiple meanings: "bank" (financial/river)</p>
                    
                    <h4>Structural Differences</h4>
                    <p>Different word orders: English (SVO) vs Japanese (SOV)</p>
                </div>
                <div>
                    <h4>Cultural Nuances</h4>
                    <p>Context-dependent meanings and cultural concepts</p>
                    
                    <h4>Idiomatic Expressions</h4>
                    <p>"It's raining cats and dogs" cannot be translated literally</p>
                </div>
            </div>
        </div>

        <!-- Slide 5: Statistical Machine Translation Introduction -->
        <div class="slide">
            <h2>Statistical Machine Translation (SMT)</h2>
            <p>Dominated MT from 1990s to early 2010s. Based on statistical models learned from large parallel corpora.</p>
            
            <div class="highlight-box">
                <h3>Core Principle: Noisy Channel Model</h3>
                <div class="equation">
                    P(target|source) = P(source|target) × P(target) / P(source)
                </div>
            </div>
            
            <ul>
                <li><strong>P(source|target):</strong> Translation Model</li>
                <li><strong>P(target):</strong> Language Model</li>
                <li><strong>P(source):</strong> Constant (ignored)</li>
            </ul>
        </div>

        <!-- Slide 6: SMT Components -->
        <div class="slide">
            <h2>SMT Key Components</h2>
            <div class="architecture-box">
                <h3>Translation Model</h3>
                <p>Maps source phrases to target phrases</p>
            </div>
            
            <div class="architecture-box">
                <h3>Language Model</h3>
                <p>Ensures fluent target language output</p>
            </div>
            
            <div class="architecture-box">
                <h3>Decoder</h3>
                <p>Searches for highest probability translation</p>
            </div>
        </div>

        <!-- Slide 7: Phrase-Based SMT -->
        <div class="slide">
            <h2>Phrase-Based SMT (PBSMT)</h2>
            <h3>Training Process:</h3>
            <ul>
                <li><strong>Word Alignment:</strong> Align words between source and target (GIZA++)</li>
                <li><strong>Phrase Extraction:</strong> Extract phrase pairs from alignments</li>
                <li><strong>Phrase Table:</strong> Calculate translation probabilities</li>
                <li><strong>Language Model:</strong> Train n-gram models on target data</li>
            </ul>
            
            <div class="equation">
                score = Σ λᵢ × hᵢ(target, source)
            </div>
            <p style="text-align: center;">Log-linear model combining multiple features</p>
        </div>

        <!-- Slide 8: SMT Advantages and Limitations -->
        <div class="slide">
            <h2>SMT: Advantages vs Limitations</h2>
            <div class="two-column">
                <div>
                    <h3 style="color: #27ae60;">Advantages</h3>
                    <ul>
                        <li>Interpretable components</li>
                        <li>Easy to add new features</li>
                        <li>Language independent</li>
                        <li>Handles rare words well</li>
                    </ul>
                </div>
                <div>
                    <h3 style="color: #e74c3c;">Limitations</h3>
                    <ul>
                        <li>Requires large parallel data</li>
                        <li>Poor long-range dependencies</li>
                        <li>Fluency issues</li>
                        <li>Extensive feature engineering</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 9: Neural Machine Translation Introduction -->
        <div class="slide">
            <h2>Neural Machine Translation (NMT)</h2>
            <p>Revolutionary approach using deep neural networks for end-to-end translation learning.</p>
            
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-year">2013-14</div>
                    <p>Early Neural</p>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">2014-16</div>
                    <p>Seq2Seq + Attention</p>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">2017+</div>
                    <p>Transformers</p>
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>Key Innovation: End-to-End Learning</h3>
                <p>No manual feature engineering required</p>
            </div>
        </div>

        <!-- Slide 10: Encoder-Decoder Architecture -->
        <div class="slide">
            <h2>Encoder-Decoder Architecture</h2>
            <div class="architecture-box">
                <h3>Encoder</h3>
                <p>Processes source sentence → fixed-size representation</p>
                <div class="equation">h₁, h₂, ..., hₙ = Encoder(x₁, x₂, ..., xₙ)</div>
            </div>
            
            <div class="architecture-box">
                <h3>Decoder</h3>
                <p>Generates target sentence word by word</p>
                <div class="equation">y₁, y₂, ..., yₘ = Decoder(h₁, h₂, ..., hₙ)</div>
            </div>
        </div>

        <!-- Slide 11: Attention Mechanism -->
        <div class="slide">
            <h2>Attention Mechanism</h2>
            <h3>Problem: Information Bottleneck</h3>
            <p>Basic encoder-decoder compresses all source information into fixed-size vector</p>
            
            <h3>Solution: Attention</h3>
            <div class="equation">
                eᵢⱼ = score(sᵢ, hⱼ)<br>
                αᵢⱼ = softmax(eᵢⱼ)<br>
                cᵢ = Σⱼ αᵢⱼhⱼ
            </div>
            
            <p><strong>Result:</strong> Decoder can focus on different source positions for each target word</p>
        </div>

        <!-- Slide 12: Transformer Architecture -->
        <div class="slide">
            <h2>Transformer: "Attention Is All You Need"</h2>
            <h3>Key Innovations:</h3>
            <ul>
                <li><strong>Self-Attention:</strong> Each position attends to all positions</li>
                <li><strong>Multi-Head Attention:</strong> Multiple attention mechanisms in parallel</li>
                <li><strong>Position Encoding:</strong> Adds positional information</li>
                <li><strong>No Recurrence:</strong> Fully parallel processing</li>
            </ul>
            
            <div class="highlight-box">
                <h3>Result: Faster Training + Better Performance</h3>
            </div>
        </div>

        <!-- Slide 13: Transformer Details -->
        <div class="slide">
            <h2>Transformer Architecture Details</h2>
            <div class="two-column">
                <div>
                    <h3>Encoder Stack</h3>
                    <ul>
                        <li>6 identical layers</li>
                        <li>Multi-head self-attention</li>
                        <li>Feedforward networks</li>
                        <li>Residual connections</li>
                        <li>Layer normalization</li>
                    </ul>
                </div>
                <div>
                    <h3>Decoder Stack</h3>
                    <ul>
                        <li>6 identical layers</li>
                        <li>Masked self-attention</li>
                        <li>Encoder-decoder attention</li>
                        <li>Feedforward networks</li>
                        <li>Residual + LayerNorm</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 14: Modern NMT Advances -->
        <div class="slide">
            <h2>Modern NMT Advances</h2>
            <div class="architecture-box">
                <h3>Subword Tokenization</h3>
                <p>BPE, SentencePiece, WordPiece - Handle rare words better</p>
            </div>
            
            <div class="architecture-box">
                <h3>Multilingual Models</h3>
                <p>Single model for multiple language pairs, zero-shot translation</p>
            </div>
            
            <div class="architecture-box">
                <h3>Massive Pre-trained Models</h3>
                <p>mT5, M2M-100, NLLB - Leverage scale for better performance</p>
            </div>
        </div>

        <!-- Slide 15: NMT Advantages and Limitations -->
        <div class="slide">
            <h2>NMT: Advantages vs Limitations</h2>
            <div class="two-column">
                <div>
                    <h3 style="color: #27ae60;">Advantages</h3>
                    <ul>
                        <li>End-to-end learning</li>
                        <li>Better fluency</li>
                        <li>Context awareness</li>
                        <li>Multilingual capabilities</li>
                    </ul>
                </div>
                <div>
                    <h3 style="color: #e74c3c;">Limitations</h3>
                    <ul>
                        <li>Data hungry</li>
                        <li>Less interpretable</li>
                        <li>Computational requirements</li>
                        <li>Hallucination issues</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 16: Performance Comparison -->
        <div class="slide">
            <h2>SMT vs NMT Performance</h2>
            <table class="comparison-table">
                <tr>
                    <th>Metric</th>
                    <th>SMT</th>
                    <th>Early NMT</th>
                    <th>Modern NMT</th>
                </tr>
                <tr>
                    <td>BLEU Score</td>
                    <td>20-30</td>
                    <td>25-35</td>
                    <td>35-50+</td>
                </tr>
                <tr>
                    <td>Fluency</td>
                    <td>Moderate</td>
                    <td>Good</td>
                    <td>Excellent</td>
                </tr>
                <tr>
                    <td>Training Time</td>
                    <td>Hours</td>
                    <td>Days</td>
                    <td>Days-Weeks</td>
                </tr>
                <tr>
                    <td>Inference Speed</td>
                    <td>Fast</td>
                    <td>Slow</td>
                    <td>Moderate</td>
                </tr>
            </table>
        </div>

        <!-- Slide 17: Error Analysis -->
        <div class="slide">
            <h2>Error Analysis: SMT vs NMT</h2>
            <div class="two-column">
                <div>
                    <h3>SMT Error Patterns</h3>
                    <ul>
                        <li><strong>Reordering:</strong> Word order issues</li>
                        <li><strong>Lexical Choice:</strong> Wrong word selection</li>
                        <li><strong>Disfluency:</strong> Unnatural phrasing</li>
                        <li><strong>Coverage:</strong> Missing translations</li>
                    </ul>
                </div>
                <div>
                    <h3>NMT Error Patterns</h3>
                    <ul>
                        <li><strong>Over-translation:</strong> Repeating phrases</li>
                        <li><strong>Under-translation:</strong> Missing content</li>
                        <li><strong>Hallucination:</strong> Adding false info</li>
                        <li><strong>Rare Words:</strong> OOV handling</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 18: Evaluation Metrics -->
        <div class="slide">
            <h2>Evaluation Metrics</h2>
            <div class="architecture-box">
                <h3>Automatic Metrics</h3>
                <ul>
                    <li><strong>BLEU:</strong> N-gram overlap with references</li>
                    <li><strong>METEOR:</strong> Incorporates synonyms and stemming</li>
                    <li><strong>BERTScore:</strong> Contextual embedding similarity</li>
                    <li><strong>COMET:</strong> Neural metric trained on human judgments</li>
                </ul>
            </div>
            
            <div class="architecture-box">
                <h3>Human Evaluation</h3>
                <ul>
                    <li><strong>Adequacy:</strong> Meaning preservation</li>
                    <li><strong>Fluency:</strong> Natural language quality</li>
                </ul>
            </div>
        </div>

        <!-- Slide 19: Real-World Applications -->
        <div class="slide">
            <h2>Real-World Applications</h2>
            <div class="two-column">
                <div>
                    <h3>Commercial Services</h3>
                    <ul>
                        <li>Google Translate</li>
                        <li>Microsoft Translator</li>
                        <li>DeepL</li>
                        <li>Amazon Translate</li>
                    </ul>
                    
                    <h3>Specialized Domains</h3>
                    <ul>
                        <li>Medical translation</li>
                        <li>Legal documents</li>
                        <li>Technical documentation</li>
                    </ul>
                </div>
                <div>
                    <h3>Integration Scenarios</h3>
                    <ul>
                        <li>Website localization</li>
                        <li>Mobile applications</li>
                        <li>Customer support</li>
                        <li>Social media</li>
                    </ul>
                    
                    <h3>Deployment Challenges</h3>
                    <ul>
                        <li>Latency requirements</li>
                        <li>Domain adaptation</li>
                        <li>Robustness</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 20: Low-Resource Translation -->
        <div class="slide">
            <h2>Low-Resource Translation</h2>
            <h3>Challenges:</h3>
            <ul>
                <li>Limited parallel data</li>
                <li>Domain mismatch</li>
                <li>Lack of linguistic resources</li>
            </ul>
            
            <h3>Solutions:</h3>
            <div class="two-column">
                <div>
                    <h4>Transfer Learning</h4>
                    <p>Pre-train on high-resource pairs, fine-tune on low-resource</p>
                </div>
                <div>
                    <h4>Multilingual Models</h4>
                    <p>Shared representations across many languages</p>
                </div>
            </div>
        </div>

        <!-- Slide 21: Multilingual NMT -->
        <div class="slide">
            <h2>Multilingual Neural Machine Translation</h2>
            <div class="highlight-box">
                <h3>One Model, Many Languages</h3>
                <p>Single neural network handles multiple language pairs</p>
            </div>
            
            <h3>Key Benefits:</h3>
            <ul>
                <li><strong>Zero-shot Translation:</strong> Translate between unseen language pairs</li>
                <li><strong>Cross-lingual Transfer:</strong> High-resource languages help low-resource</li>
                <li><strong>Efficiency:</strong> Single model deployment</li>
                <li><strong>Consistency:</strong> Shared representations across languages</li>
            </ul>
        </div>

        <!-- Slide 22: Current Trends -->
        <div class="slide">
            <h2>Current Trends and Future Directions</h2>
            <div class="architecture-box">
                <h3>Large Language Models</h3>
                <p>GPT-3, PaLM, ChatGPT showing strong translation capabilities</p>
            </div>
            
            <div class="architecture-box">
                <h3>Multimodal Translation</h3>
                <p>Incorporating visual context for better translation</p>
            </div>
            
            <div class="architecture-box">
                <h3>Document-Level Translation</h3>
                <p>Context beyond sentence boundaries</p>
            </div>
        </div>

        <!-- Slide 23: Ethical Considerations -->
        <div class="slide">
            <h2>Ethical Considerations</h2>
            <div class="two-column">
                <div>
                    <h3>Bias and Fairness</h3>
                    <ul>
                        <li>Gender bias in translations</li>
                        <li>Cultural stereotypes</li>
                        <li>Representation issues</li>
                    </ul>
                    
                    <h3>Privacy</h3>
                    <ul>
                        <li>Sensitive content handling</li>
                        <li>Data retention policies</li>
                        <li>On-device vs cloud processing</li>
                    </ul>
                </div>
                <div>
                    <h3>Accessibility</h3>
                    <ul>
                        <li>Language preservation</li>
                        <li>Digital divide</li>
                        <li>Equal access to technology</li>
                    </ul>
                    
                    <h3>Quality and Safety</h3>
                    <ul>
                        <li>Misinformation propagation</li>
                        <li>Critical domain accuracy</li>
                        <li>User awareness of limitations</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 24: Practical Implementation -->
        <div class="slide">
            <h2>Practical Implementation Considerations</h2>
            <h3>System Design Choices:</h3>
            <ul>
                <li><strong>Model Selection:</strong> SMT for interpretability, NMT for quality</li>
                <li><strong>Data Requirements:</strong> Parallel corpora, monolingual data</li>
                <li><strong>Computational Resources:</strong> Training vs inference costs</li>
                <li><strong>Latency vs Quality:</strong> Real-time vs batch processing</li>
            </ul>
            
            <div class="code-block">
# Example: Simple NMT inference
model = load_pretrained_model('transformer-base')
source_text = "Hello, how are you?"
translation = model.translate(source_text, target_lang='es')
print(translation)  # "Hola, ¿cómo estás?"
            </div>
        </div>

        <!-- Slide 25: Hands-on Learning -->
        <div class="slide">
            <h2>Hands-on Learning Opportunities</h2>
            <div class="two-column">
                <div>
                    <h3>SMT Implementation</h3>
                    <ul>
                        <li>Word alignment with GIZA++</li>
                        <li>Phrase extraction</li>
                        <li>Language model training</li>
                        <li>Beam search decoder</li>
                    </ul>
                    
                    <h3>Tools and Frameworks</h3>
                    <ul>
                        <li>Moses (SMT)</li>
                        <li>OpenNMT (NMT)</li>
                        <li>Fairseq (Facebook)</li>
                        <li>Transformers (Hugging Face)</li>
                    </ul>
                </div>
                <div>
                    <h3>NMT Implementation</h3>
                    <ul>
                        <li>Seq2seq with attention</li>
                        <li>Transformer architecture</li>
                        <li>Subword tokenization</li>
                        <li>Multilingual models</li>
                    </ul>
                    
                    <h3>Datasets</h3>
                    <ul>
                        <li>WMT shared tasks</li>
                        <li>OPUS parallel corpora</li>
                        <li>UN Parallel Corpus</li>
                        <li>OpenSubtitles</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 26: Assessment and Evaluation -->
        <div class="slide">
            <h2>Assessment Framework</h2>
            <h3>Knowledge Areas:</h3>
            <div class="two-column">
                <div>
                    <h4>Theoretical Understanding</h4>
                    <ul>
                        <li>SMT mathematical foundations</li>
                        <li>Neural network architectures</li>
                        <li>Attention mechanisms</li>
                        <li>Evaluation metrics</li>
                    </ul>
                </div>
                <div>
                    <h4>Practical Skills</h4>
                    <ul>
                        <li>System implementation</li>
                        <li>Model training and tuning</li>
                        <li>Error analysis</li>
                        <li>Performance optimization</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 27: Research Opportunities -->
        <div class="slide">
            <h2>Research Opportunities</h2>
            <div class="architecture-box">
                <h3>Open Research Problems</h3>
                <ul>
                    <li><strong>Discourse-Level Translation:</strong> Context beyond sentences</li>
                    <li><strong>Interactive Translation:</strong> Human-AI collaboration</li>
                    <li><strong>Controllable Translation:</strong> Style, formality, domain control</li>
                    <li><strong>Explanation Generation:</strong> Why certain translations are chosen</li>
                </ul>
            </div>
            
            <div class="architecture-box">
                <h3>Emerging Areas</h3>
                <ul>
                    <li>Simultaneous translation (speech)</li>
                    <li>Code-switching and multilingual texts</li>
                    <li>Translation for endangered languages</li>
                </ul>
            </div>
        </div>

        <!-- Slide 28: Industry Applications -->
        <div class="slide">
            <h2>Industry Applications and Career Paths</h2>
            <div class="two-column">
                <div>
                    <h3>Career Opportunities</h3>
                    <ul>
                        <li><strong>Research Scientist:</strong> Advancing MT algorithms</li>
                        <li><strong>ML Engineer:</strong> Deploying MT systems</li>
                        <li><strong>Product Manager:</strong> Translation product strategy</li>
                        <li><strong>Localization Specialist:</strong> Content adaptation</li>
                    </ul>
                </div>
                <div>
                    <h3>Industry Sectors</h3>
                    <ul>
                        <li><strong>Technology:</strong> Google, Microsoft, Meta</li>
                        <li><strong>E-commerce:</strong> Amazon, eBay</li>
                        <li><strong>Media:</strong> Netflix, news organizations</li>
                        <li><strong>Enterprise:</strong> SAP, Oracle</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Slide 29: Key Takeaways -->
        <div class="slide">
            <h2>Key Takeaways</h2>
            <div class="highlight-box">
                <h3>Evolution of Machine Translation</h3>
                <p>From statistical to neural approaches, each with unique strengths</p>
            </div>
            
            <ul>
                <li><strong>SMT:</strong> Interpretable, feature-rich, good for low-resource scenarios</li>
                <li><strong>NMT:</strong> End-to-end learning, better fluency, context awareness</li>
                <li><strong>Transformers:</strong> Revolutionized the field with attention mechanisms</li>
                <li><strong>Current Focus:</strong> Multilingual models, efficiency, ethical considerations</li>
                <li><strong>Future:</strong> Integration with large language models, multimodal translation</li>
            </ul>
        </div>

        <!-- Slide 30: Conclusion and Next Steps -->
        <div class="slide">
            <h2>Conclusion and Next Steps</h2>
            <div class="two-column">
                <div>
                    <h3>What We've Covered</h3>
                    <ul>
                        <li>Historical development of MT</li>
                        <li>Statistical approaches and components</li>
                        <li>Neural architectures and attention</li>
                        <li>Comparative analysis</li>
                        <li>Real-world applications</li>
                    </ul>
                </div>
                <div>
                    <h3>Next Steps</h3>
                    <ul>
                        <li>Implement hands-on projects</li>
                        <li>Explore research papers</li>
                        <li>Experiment with existing tools</li>
                        <li>Consider ethical implications</li>
                        <li>Stay updated with latest advances</li>
                    </ul>
                </div>
            </div>
            
            <div class="highlight-box">
                <h3>Machine Translation: Bridging Language Barriers</h3>
                <p>Enabling global communication and understanding</p>
            </div>
        </div>
    </div>

    <div class="slide-counter">
        <span id="slideNumber">1</span> / <span id="totalSlides">30</span>
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">Previous</button>
        <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">Next</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('totalSlides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            document.getElementById('slideNumber').textContent = currentSlide + 1;
            document.getElementById('prevBtn').disabled = currentSlide === 0;
            document.getElementById('nextBtn').disabled = currentSlide === totalSlides - 1;
            
            // Update progress bar
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }

        function changeSlide(direction) {
            if (direction === 1 && currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            } else if (direction === -1 && currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                e.preventDefault();
                changeSlide(1);
            } else if (e.key === 'ArrowLeft') {
                e.preventDefault();
                changeSlide(-1);
            } else if (e.key === 'Home') {
                e.preventDefault();
                showSlide(0);
            } else if (e.key === 'End') {
                e.preventDefault();
                showSlide(totalSlides - 1);
            }
        });

        // Initialize
        showSlide(0);

        // Prevent context menu on right click
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // Fullscreen toggle on F11
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F11') {
                e.preventDefault();
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    document.documentElement.requestFullscreen();
                }
            }
        });
    </script>
</body>
</html>